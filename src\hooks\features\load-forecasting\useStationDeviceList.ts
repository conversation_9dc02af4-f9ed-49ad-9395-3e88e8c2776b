import { useBusbarLoadStore, useCommonStore, useSiderbarStore } from '@/stores'
import {
  BusbarLoadService,
  type BusbarLoadExistDeviceListResponse,
} from '@/utils/api/services/busbarLoad'
import { formatTime } from '@/utils/tools'
import type { TreeSelectOption } from 'naive-ui'

/**
 * 查询母线负荷下的相关可查询设备列表
 * @returns
 */
export const useStationDeviceList = () => {
  const busbarLoadStore = useBusbarLoadStore()
  const siderbarStore = useSiderbarStore()
  const commonStore = useCommonStore()

  const fetchStationDeviceList = async (timeRange: [number, number], regionCode?: string) => {
    commonStore.loading = true
    try {
      const response = await BusbarLoadService.getBusbarLoadExistDeviceList({
        startTime: formatTime(timeRange[0], 'YYYY-MM-DD HH:mm:ss'),
        endTime: formatTime(timeRange[1], 'YYYY-MM-DD HH:mm:ss'),
        regionCode,
      })
      formatData(response)

      siderbarStore.setCityOptions(response)
    } catch (error) {
      console.error('获取设备列表失败:', error)
    } finally {
      commonStore.loading = false
    }
  }

  const formatData = (data: BusbarLoadExistDeviceListResponse[]) => {
    const list: TreeSelectOption[] = []
    data.forEach((item) => {
      const treeSelectOption = {
        ...item,
        label: item.name,
        key: item.id,
        stationId: item.id,
        children:
          item.deviceList.map((device) => ({
            ...device,
            label: device.name,
            key: device.id,
            deviceId: device.id,
          })) || [],
      }
      list.push(treeSelectOption)
    })
    busbarLoadStore.stationDeviceList = list
  }

  return {
    fetchStationDeviceList,
  }
}
